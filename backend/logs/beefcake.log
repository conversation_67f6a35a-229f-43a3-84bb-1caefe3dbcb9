2025-07-16 10:48:20.164 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 10:48:20.407 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 10:48:20.407 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 10:48:20.407 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 10:48:20.483 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 10:48:20.489 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 10:48:20.500 [main] INFO  o.f.c.i.s.JdbcTableSchemaHistory - Schema history table `beefcake`.`flyway_schema_history` does not exist yet
2025-07-16 10:48:20.501 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.006s)
2025-07-16 10:48:20.514 [main] INFO  o.f.c.i.s.JdbcTableSchemaHistory - Creating Schema History table `beefcake`.`flyway_schema_history` ...
2025-07-16 10:48:20.546 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: << Empty Schema >>
2025-07-16 10:48:20.560 [main] INFO  o.f.core.internal.command.DbMigrate - Migrating schema `beefcake` to version "1 - Create initial tables"
2025-07-16 10:48:20.776 [main] INFO  o.f.core.internal.command.DbMigrate - Successfully applied 1 migration to schema `beefcake`, now at version v1 (execution time 00:00.194s)
2025-07-16 10:48:20.784 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 10:48:20.784 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 10:48:20.844 [main] INFO  Application - Application started in 0.691 seconds.
2025-07-16 10:48:20.844 [main] INFO  Application - Application started: io.ktor.server.application.Application@a098d76
2025-07-16 10:48:20.910 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 10:51:35.410 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@a098d76
2025-07-16 10:51:35.412 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@a098d76
2025-07-16 10:51:36.321 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 10:51:36.492 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 10:51:36.492 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 10:51:36.492 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 10:51:36.549 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 10:51:36.555 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 10:51:36.570 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.011s)
2025-07-16 10:51:36.577 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 10:51:36.578 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 10:51:36.582 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 10:51:36.582 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 10:51:36.617 [main] INFO  Application - Application started in 0.307 seconds.
2025-07-16 10:51:36.617 [main] INFO  Application - Application started: io.ktor.server.application.Application@4760f169
2025-07-16 10:51:36.661 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 10:54:59.052 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@4760f169
2025-07-16 10:54:59.054 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@4760f169
2025-07-16 10:55:00.702 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 10:55:00.827 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 10:55:00.878 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6dc1484
2025-07-16 10:55:00.879 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 10:55:00.954 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 10:55:00.954 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 10:55:00.954 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 10:55:00.969 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 10:55:00.975 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 10:55:00.990 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.011s)
2025-07-16 10:55:00.997 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 10:55:00.998 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 10:55:01.002 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 10:55:01.002 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 10:55:01.031 [main] INFO  Application - Application started in 0.34 seconds.
2025-07-16 10:55:01.031 [main] INFO  Application - Application started: io.ktor.server.application.Application@d5d5353
2025-07-16 10:55:01.106 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 10:55:11.212 [eventLoopGroupProxy-4-1] INFO  com.beefcake.services.UserService - 用户注册成功: test
2025-07-16 10:55:11.222 [eventLoopGroupProxy-4-1] INFO  Application - 201 Created: POST /api/auth/register - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:34.793 [eventLoopGroupProxy-4-2] INFO  com.beefcake.services.UserService - 用户登录成功: test
2025-07-16 10:55:34.795 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: POST /api/auth/login - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:34.890 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/tasks/statistics - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:41.422 [eventLoopGroupProxy-4-4] ERROR TaskRoutes - 获取任务列表异常
java.lang.IllegalStateException: DESIGN of kotlin.String is not valid for enum TaskType
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:876)
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:865)
	at org.jetbrains.exposed.sql.ResultRow.rawToColumnValue(ResultRow.kt:82)
	at org.jetbrains.exposed.sql.ResultRow.access$rawToColumnValue(ResultRow.kt:8)
	at org.jetbrains.exposed.sql.ResultRow$getInternal$result$1$1.invoke(ResultRow.kt:69)
	at org.jetbrains.exposed.sql.vendors.DatabaseDialectKt.withDialect(DatabaseDialect.kt:189)
	at org.jetbrains.exposed.sql.ResultRow.getInternal(ResultRow.kt:68)
	at org.jetbrains.exposed.sql.ResultRow.get(ResultRow.kt:24)
	at com.beefcake.repositories.TaskRepository.resultRowToTask(TaskRepository.kt:209)
	at com.beefcake.repositories.TaskRepository.access$resultRowToTask(TaskRepository.kt:12)
	at com.beefcake.repositories.TaskRepository$findAll$2.invokeSuspend(TaskRepository.kt:151)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:62)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:187)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
2025-07-16 10:55:41.429 [eventLoopGroupProxy-4-4] INFO  Application - 400 Bad Request: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:42.454 [eventLoopGroupProxy-4-5] ERROR TaskRoutes - 获取任务列表异常
java.lang.IllegalStateException: DESIGN of kotlin.String is not valid for enum TaskType
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:876)
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:865)
	at org.jetbrains.exposed.sql.ResultRow.rawToColumnValue(ResultRow.kt:82)
	at org.jetbrains.exposed.sql.ResultRow.access$rawToColumnValue(ResultRow.kt:8)
	at org.jetbrains.exposed.sql.ResultRow$getInternal$result$1$1.invoke(ResultRow.kt:69)
	at org.jetbrains.exposed.sql.vendors.DatabaseDialectKt.withDialect(DatabaseDialect.kt:189)
	at org.jetbrains.exposed.sql.ResultRow.getInternal(ResultRow.kt:68)
	at org.jetbrains.exposed.sql.ResultRow.get(ResultRow.kt:24)
	at com.beefcake.repositories.TaskRepository.resultRowToTask(TaskRepository.kt:209)
	at com.beefcake.repositories.TaskRepository.access$resultRowToTask(TaskRepository.kt:12)
	at com.beefcake.repositories.TaskRepository$findAll$2.invokeSuspend(TaskRepository.kt:151)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:62)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:187)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
2025-07-16 10:55:42.456 [eventLoopGroupProxy-4-5] INFO  Application - 400 Bad Request: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:45.223 [eventLoopGroupProxy-4-6] ERROR TaskRoutes - 获取任务列表异常
java.lang.IllegalStateException: DESIGN of kotlin.String is not valid for enum TaskType
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:876)
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:865)
	at org.jetbrains.exposed.sql.ResultRow.rawToColumnValue(ResultRow.kt:82)
	at org.jetbrains.exposed.sql.ResultRow.access$rawToColumnValue(ResultRow.kt:8)
	at org.jetbrains.exposed.sql.ResultRow$getInternal$result$1$1.invoke(ResultRow.kt:69)
	at org.jetbrains.exposed.sql.vendors.DatabaseDialectKt.withDialect(DatabaseDialect.kt:189)
	at org.jetbrains.exposed.sql.ResultRow.getInternal(ResultRow.kt:68)
	at org.jetbrains.exposed.sql.ResultRow.get(ResultRow.kt:24)
	at com.beefcake.repositories.TaskRepository.resultRowToTask(TaskRepository.kt:209)
	at com.beefcake.repositories.TaskRepository.access$resultRowToTask(TaskRepository.kt:12)
	at com.beefcake.repositories.TaskRepository$findAll$2.invokeSuspend(TaskRepository.kt:151)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:62)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:187)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
2025-07-16 10:55:45.224 [eventLoopGroupProxy-4-6] INFO  Application - 400 Bad Request: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:45.731 [eventLoopGroupProxy-4-7] INFO  Application - 200 OK: GET /api/tasks/statistics - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:56:46.588 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@d5d5353
2025-07-16 10:56:46.589 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@d5d5353
2025-07-16 10:56:48.367 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 10:56:48.467 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 10:56:48.529 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d467c87
2025-07-16 10:56:48.529 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 10:56:48.623 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 10:56:48.624 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 10:56:48.624 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 10:56:48.639 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 10:56:48.644 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 10:56:48.663 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.014s)
2025-07-16 10:56:48.668 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 10:56:48.670 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 10:56:48.673 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 10:56:48.673 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 10:56:48.701 [main] INFO  Application - Application started in 0.35 seconds.
2025-07-16 10:56:48.702 [main] INFO  Application - Application started: io.ktor.server.application.Application@aaa0f76
2025-07-16 10:56:48.771 [DefaultDispatcher-worker-2] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 10:56:55.969 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:59:19.394 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@aaa0f76
2025-07-16 10:59:19.397 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@aaa0f76
2025-07-16 10:59:20.719 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 10:59:20.836 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 10:59:20.896 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d467c87
2025-07-16 10:59:20.897 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 10:59:20.983 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 10:59:20.983 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 10:59:20.983 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 10:59:20.998 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 10:59:21.004 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 10:59:21.021 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.013s)
2025-07-16 10:59:21.028 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 10:59:21.029 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 10:59:21.033 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 10:59:21.033 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 10:59:21.085 [main] INFO  Application - Application started in 0.376 seconds.
2025-07-16 10:59:21.086 [main] INFO  Application - Application started: io.ktor.server.application.Application@aaa0f76
2025-07-16 10:59:21.179 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
